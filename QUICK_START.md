# 🚀 数字人女友App - 快速开始指南

## 📋 安装前检查

确保您的系统满足以下要求：
- ✅ Windows 10/11 (64位)
- ✅ 至少 4GB 可用磁盘空间
- ✅ 稳定的网络连接

## 🎯 一键安装 (推荐)

### 方法1: 使用一键安装脚本

1. **以管理员身份运行**
   ```
   右键点击 INSTALL_FLUTTER.bat → "以管理员身份运行"
   ```

2. **选择安装选项**
   - 选择 `1` 进行完整安装 (推荐)
   - 等待安装完成

3. **重启命令提示符**
   - 关闭当前命令提示符
   - 重新打开新的命令提示符

### 方法2: 手动安装

如果自动安装失败，请按照以下步骤：

1. **下载Flutter SDK**
   - 访问: https://flutter.dev/docs/get-started/install/windows
   - 下载最新稳定版
   - 解压到 `C:\flutter`

2. **配置环境变量**
   - 添加 `C:\flutter\bin` 到系统PATH

3. **安装开发工具**
   - Android Studio: https://developer.android.com/studio
   - VS Code: https://code.visualstudio.com/

## ✅ 验证安装

运行验证脚本检查安装状态：
```bash
scripts\verify_flutter.bat
```

或手动验证：
```bash
flutter --version
flutter doctor
```

期望看到类似输出：
```
Flutter 3.16.0 • channel stable
[✓] Flutter (Channel stable, 3.16.0)
[✓] Windows Version
[✓] Android toolchain
[✓] Chrome - develop for the web
[✓] Visual Studio
[✓] Android Studio
[✓] VS Code
```

## 🛠️ 项目设置

1. **进入项目目录**
   ```bash
   cd "C:\Users\<USER>\Pictures\数字人女友app"
   ```

2. **运行项目设置脚本**
   ```bash
   scripts\setup.bat
   ```

3. **或手动设置**
   ```bash
   flutter pub get
   flutter doctor
   ```

## 📱 运行应用

### 使用Android模拟器

1. **启动Android Studio**
2. **打开AVD Manager**
   - Tools → AVD Manager
3. **创建虚拟设备**
   - Create Virtual Device
   - 选择设备型号 (推荐: Pixel 4)
   - 选择系统镜像 (推荐: API 30+)
4. **启动模拟器**

### 使用真机调试

1. **启用开发者选项**
   - 设置 → 关于手机 → 连续点击版本号7次
2. **启用USB调试**
   - 设置 → 开发者选项 → USB调试
3. **连接手机到电脑**

### 运行应用

```bash
# 检查连接的设备
flutter devices

# 运行调试版本
flutter run

# 运行发布版本
flutter run --release
```

## 🔧 常见问题解决

### 问题1: 'flutter' 不是内部或外部命令

**解决方案:**
1. 检查Flutter是否正确安装到 `C:\flutter`
2. 检查环境变量PATH是否包含 `C:\flutter\bin`
3. 重启命令提示符

### 问题2: Android licenses not accepted

**解决方案:**
```bash
flutter doctor --android-licenses
# 按 'y' 接受所有许可证
```

### 问题3: 无法连接到设备

**解决方案:**
- 确保USB调试已启用
- 尝试不同的USB线缆
- 重启ADB: `adb kill-server && adb start-server`
- 使用模拟器进行测试

### 问题4: Gradle下载缓慢

**解决方案:**
1. 配置Gradle镜像 (中国用户)
2. 使用VPN或代理
3. 等待首次构建完成 (可能需要10-30分钟)

## 📚 开发资源

### 官方文档
- Flutter官网: https://flutter.dev/
- Flutter中文网: https://flutter.cn/
- Dart语言: https://dart.dev/

### 开发工具
- Android Studio: 完整的IDE
- VS Code: 轻量级编辑器
- Flutter Inspector: UI调试工具

### 学习资源
- Flutter官方教程
- Flutter实战
- Dart语言教程

## 🎉 开始开发

安装完成后，您可以：

1. **修改应用配置**
   - 编辑 `lib/services/ai_service.dart` 配置后端API
   - 编辑 `lib/services/websocket_service.dart` 配置WebSocket

2. **自定义UI**
   - 修改 `lib/utils/app_theme.dart` 自定义主题
   - 编辑各个页面文件自定义界面

3. **添加功能**
   - 在 `lib/` 目录下添加新的功能模块
   - 使用Provider进行状态管理

4. **测试应用**
   - 编写单元测试
   - 进行真机测试

## 📞 获取帮助

如果遇到问题：
1. 查看 `FLUTTER_INSTALL_GUIDE.md` 详细安装指南
2. 运行 `flutter doctor -v` 获取详细诊断信息
3. 查看Flutter官方文档
4. 在GitHub上提交Issue

祝您开发愉快！🎊
