@echo off
title Flutter环境验证
color 0E

echo ========================================
echo        Flutter环境验证脚本
echo ========================================
echo.

echo [1/8] 检查Flutter安装...
flutter --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ Flutter已安装
    flutter --version
) else (
    echo ✗ Flutter未安装或未在PATH中
    echo.
    echo 解决方案:
    echo 1. 运行 INSTALL_FLUTTER.bat 安装Flutter
    echo 2. 或手动添加Flutter到系统PATH
    echo.
    goto END
)

echo.
echo [2/8] 检查Dart SDK...
dart --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ Dart SDK可用
    dart --version
) else (
    echo ✗ Dart SDK不可用
)

echo.
echo [3/8] 检查Git...
git --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ Git已安装
    git --version
) else (
    echo ✗ Git未安装
    echo 下载地址: https://git-scm.com/download/win
)

echo.
echo [4/8] 检查VS Code...
where code >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ VS Code已安装
    code --version | head -1
) else (
    echo ✗ VS Code未安装或未在PATH中
    echo 下载地址: https://code.visualstudio.com/
)

echo.
echo [5/8] 检查Android Studio...
set ANDROID_STUDIO_FOUND=0
if exist "%ProgramFiles%\Android\Android Studio\bin\studio64.exe" (
    echo ✓ Android Studio已安装 (Program Files)
    set ANDROID_STUDIO_FOUND=1
)
if exist "%LOCALAPPDATA%\JetBrains\Toolbox\apps\AndroidStudio" (
    echo ✓ Android Studio已安装 (JetBrains Toolbox)
    set ANDROID_STUDIO_FOUND=1
)
if %ANDROID_STUDIO_FOUND%==0 (
    echo ✗ Android Studio未找到
    echo 下载地址: https://developer.android.com/studio
)

echo.
echo [6/8] 检查Android SDK...
if defined ANDROID_HOME (
    echo ✓ ANDROID_HOME已设置: %ANDROID_HOME%
) else (
    echo ✗ ANDROID_HOME未设置
)

echo.
echo [7/8] 检查项目依赖...
if exist "%~dp0..\pubspec.yaml" (
    echo ✓ 找到Flutter项目
    cd /d "%~dp0.."
    if exist "pubspec.lock" (
        echo ✓ 依赖已安装
    ) else (
        echo ! 依赖未安装，运行: flutter pub get
    )
) else (
    echo ✗ 未找到Flutter项目文件
)

echo.
echo [8/8] 运行Flutter Doctor...
echo ========================================
flutter doctor -v

echo.
echo ========================================
echo           验证结果总结
echo ========================================
echo.

:: 检查Flutter Doctor结果
flutter doctor >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 环境验证通过
    echo.
    echo 可以开始开发了! 运行以下命令:
    echo   cd "%~dp0.."
    echo   flutter pub get
    echo   flutter run
) else (
    echo ! 环境存在问题
    echo.
    echo 常见解决方案:
    echo 1. 运行: flutter doctor --android-licenses
    echo 2. 在Android Studio中安装Flutter插件
    echo 3. 创建Android虚拟设备 (AVD)
    echo 4. 确保USB调试已启用 (真机调试)
)

echo.
:END
echo 按任意键退出...
pause >nul
