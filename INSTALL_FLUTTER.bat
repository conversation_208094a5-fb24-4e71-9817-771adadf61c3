@echo off
title Flutter环境一键安装
color 0B

echo ========================================
echo      Flutter环境一键安装脚本
echo ========================================
echo.
echo 此脚本将帮助您安装Flutter开发环境
echo.
echo 安装内容:
echo - Flutter SDK
echo - 开发工具 (VS Code/Android Studio)
echo - 环境变量配置
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限!
    echo.
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 检测到管理员权限 ✓
echo.

:MENU
echo ========================================
echo           安装选项菜单
echo ========================================
echo.
echo 1. 完整安装 (推荐)
echo    - Flutter SDK + 开发工具
echo.
echo 2. 仅安装Flutter SDK
echo.
echo 3. 仅安装开发工具
echo.
echo 4. 查看安装指南
echo.
echo 5. 验证现有安装
echo.
echo 0. 退出
echo.
set /p choice="请选择安装选项 (0-5): "

if "%choice%"=="1" goto FULL_INSTALL
if "%choice%"=="2" goto FLUTTER_ONLY
if "%choice%"=="3" goto TOOLS_ONLY
if "%choice%"=="4" goto SHOW_GUIDE
if "%choice%"=="5" goto VERIFY_INSTALL
if "%choice%"=="0" goto EXIT
goto MENU

:FULL_INSTALL
echo.
echo ========================================
echo           开始完整安装
echo ========================================
echo.
echo 步骤1: 安装Flutter SDK...
call "%~dp0scripts\install_flutter_simple.bat"
echo.
echo 步骤2: 安装开发工具...
powershell -ExecutionPolicy Bypass -File "%~dp0scripts\install_dev_tools.ps1"
goto VERIFY_INSTALL

:FLUTTER_ONLY
echo.
echo ========================================
echo         仅安装Flutter SDK
echo ========================================
echo.
call "%~dp0scripts\install_flutter_simple.bat"
goto VERIFY_INSTALL

:TOOLS_ONLY
echo.
echo ========================================
echo          仅安装开发工具
echo ========================================
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0scripts\install_dev_tools.ps1"
goto VERIFY_INSTALL

:SHOW_GUIDE
echo.
echo ========================================
echo           安装指南
echo ========================================
echo.
echo 详细安装指南请查看: FLUTTER_INSTALL_GUIDE.md
echo.
echo 快速安装步骤:
echo 1. 运行此脚本选择"完整安装"
echo 2. 重启命令提示符
echo 3. 运行: flutter doctor
echo 4. 运行: flutter doctor --android-licenses
echo 5. 进入项目目录运行: flutter pub get
echo 6. 运行: flutter run
echo.
pause
goto MENU

:VERIFY_INSTALL
echo.
echo ========================================
echo           验证安装
echo ========================================
echo.
echo 正在验证Flutter安装...
echo.

:: 检查Flutter
flutter --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ Flutter已安装
    flutter --version
) else (
    echo ✗ Flutter未安装或未在PATH中
    echo   请重新启动命令提示符后再试
)

echo.
echo 运行Flutter Doctor检查...
echo.
flutter doctor

echo.
echo ========================================
echo           下一步操作
echo ========================================
echo.
echo 1. 如果看到错误，请按照提示解决
echo 2. 运行: flutter doctor --android-licenses
echo 3. 进入项目目录: cd "%~dp0"
echo 4. 安装项目依赖: flutter pub get
echo 5. 运行项目: flutter run
echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用Flutter安装脚本!
echo.
echo 如需帮助，请查看:
echo - FLUTTER_INSTALL_GUIDE.md (详细安装指南)
echo - README.md (项目说明)
echo.
pause
exit /b 0
