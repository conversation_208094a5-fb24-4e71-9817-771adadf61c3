import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';

import 'screens/splash_screen.dart';
import 'screens/chat_screen.dart';
import 'services/ai_service.dart';
import 'services/websocket_service.dart';
import 'providers/chat_provider.dart';
import 'providers/user_provider.dart';
import 'utils/app_theme.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ChatProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        Provider(create: (_) => AIService()),
        Provider(create: (_) => WebSocketService()),
      ],
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GetMaterialApp(
            title: '数字人女友',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            home: const SplashScreen(),
            getPages: [
              GetPage(name: '/', page: () => const SplashScreen()),
              GetPage(name: '/chat', page: () => const ChatScreen()),
            ],
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
