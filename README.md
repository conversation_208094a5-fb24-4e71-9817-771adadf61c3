# 数字人女友 App

一个基于Flutter开发的AI数字人女友应用，支持文本和语音交互。

## 功能特性

- 🤖 AI智能对话
- 🎤 语音消息支持
- 💬 实时聊天界面
- 🌐 WebSocket实时通信
- 🎨 精美的UI设计
- 📱 跨平台支持 (iOS/Android)

## 技术栈

### 前端 (Flutter)
- **Flutter**: 跨平台移动应用框架
- **Provider**: 状态管理
- **GetX**: 路由管理和依赖注入
- **WebSocket**: 实时通信
- **Dio**: HTTP网络请求
- **flutter_webrtc**: 音视频通信
- **record**: 音频录制
- **just_audio**: 音频播放

### 后端 (需要单独实现)
- **Python FastAPI**: 后端API服务
- **WebSocket**: 实时通信
- **AI模型**: 对话生成
- **WebRTC**: 音视频处理

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── message.dart         # 消息模型
│   └── user.dart           # 用户模型
├── providers/               # 状态管理
│   ├── chat_provider.dart   # 聊天状态
│   └── user_provider.dart   # 用户状态
├── screens/                 # 页面
│   ├── splash_screen.dart   # 启动页
│   ├── welcome_screen.dart  # 欢迎页
│   └── chat_screen.dart     # 聊天页
├── services/                # 服务层
│   ├── ai_service.dart      # AI服务
│   └── websocket_service.dart # WebSocket服务
├── widgets/                 # UI组件
│   ├── message_bubble.dart  # 消息气泡
│   ├── chat_input.dart      # 聊天输入框
│   └── connection_status.dart # 连接状态
└── utils/                   # 工具类
    └── app_theme.dart       # 主题配置
```

## 安装和运行

### 前置要求

1. 安装Flutter SDK (>=3.10.0)
2. 安装Android Studio或Xcode
3. 配置开发环境

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd digital_girlfriend_app
```

2. 安装依赖
```bash
flutter pub get
```

3. 运行应用
```bash
# Android
flutter run

# iOS
flutter run -d ios
```

## 配置说明

### 后端API配置

在 `lib/services/ai_service.dart` 中修改后端API地址：

```dart
static const String baseUrl = 'http://your-backend-url:8000';
```

在 `lib/services/websocket_service.dart` 中修改WebSocket地址：

```dart
static const String wsUrl = 'ws://your-backend-url:8000/ws';
```

### 权限配置

应用需要以下权限：
- 麦克风权限 (录制语音)
- 网络权限 (API通信)
- 存储权限 (保存文件)

权限已在以下文件中配置：
- Android: `android/app/src/main/AndroidManifest.xml`
- iOS: `ios/Runner/Info.plist`

## 开发指南

### 添加新功能

1. 在相应的目录下创建新文件
2. 更新状态管理 (Provider)
3. 添加UI组件
4. 测试功能

### 自定义主题

在 `lib/utils/app_theme.dart` 中修改颜色和样式配置。

### API集成

在 `lib/services/` 目录下添加新的服务类。

## 构建发布

### Android
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## 注意事项

1. 确保后端服务正常运行
2. 检查网络连接和权限
3. 测试不同设备的兼容性
4. 遵循应用商店发布规范

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
