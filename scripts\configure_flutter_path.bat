@echo off
title 配置Flutter环境变量
color 0A

echo ========================================
echo        配置Flutter环境变量
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限来配置系统环境变量
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

echo 正在配置Flutter环境变量...
echo.

:: 检查Flutter安装
if not exist "C:\flutter\bin\flutter.bat" (
    echo 错误: 未找到Flutter安装文件
    echo 请确保Flutter已正确解压到 C:\flutter
    pause
    exit /b 1
)

echo ✓ 找到Flutter安装: C:\flutter\bin\flutter.bat
echo.

:: 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set CURRENT_PATH=%%b

:: 检查Flutter是否已在PATH中
echo %CURRENT_PATH% | findstr /i "flutter\bin" >nul
if %errorLevel% equ 0 (
    echo ✓ Flutter已在系统PATH中
    echo.
) else (
    echo 正在添加Flutter到系统PATH...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%CURRENT_PATH%;C:\flutter\bin" /f >nul
    if %errorLevel% equ 0 (
        echo ✓ 环境变量配置成功
        echo.
    ) else (
        echo ✗ 环境变量配置失败
        echo 请手动添加 C:\flutter\bin 到系统PATH
        pause
        exit /b 1
    )
)

:: 刷新环境变量
echo 正在刷新环境变量...
set PATH=%PATH%;C:\flutter\bin

echo ========================================
echo        配置完成
echo ========================================
echo.
echo 请重新启动命令提示符，然后运行:
echo   flutter --version
echo   flutter doctor
echo.
pause
