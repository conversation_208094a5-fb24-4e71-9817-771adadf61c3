@echo off
chcp 65001 >nul
title Android Studio安装监控
color 0B

echo ========================================
echo      Android Studio安装监控
echo ========================================
echo.

:MONITOR_LOOP
cls
echo ========================================
echo      Android Studio安装监控
echo ========================================
echo.
echo 当前时间: %date% %time%
echo.

echo [1/4] 检查下载文件...
if exist "%USERPROFILE%\Downloads\android-studio*.exe" (
    echo ✓ 找到Android Studio安装文件
    for %%f in ("%USERPROFILE%\Downloads\android-studio*.exe") do (
        echo   文件: %%~nxf
        echo   大小: %%~zf 字节
    )
) else (
    echo ⏳ 等待下载Android Studio安装文件...
    echo   下载地址: https://developer.android.com/studio
)

echo.
echo [2/4] 检查安装进程...
tasklist /FI "IMAGENAME eq android-studio*.exe" 2>nul | find "android-studio" >nul
if %errorLevel% equ 0 (
    echo ✓ Android Studio安装程序正在运行
) else (
    echo ⏳ 安装程序未运行
)

echo.
echo [3/4] 检查安装目录...
if exist "%ProgramFiles%\Android\Android Studio" (
    echo ✓ Android Studio已安装到: %ProgramFiles%\Android\Android Studio
    if exist "%ProgramFiles%\Android\Android Studio\bin\studio64.exe" (
        echo ✓ 主程序文件存在
    )
) else (
    echo ⏳ Android Studio尚未安装
)

echo.
echo [4/4] 检查Android SDK...
if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo ✓ Android SDK已安装: %LOCALAPPDATA%\Android\Sdk
) else if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
    echo ✓ Android SDK已安装: %USERPROFILE%\AppData\Local\Android\Sdk
) else (
    echo ⏳ Android SDK尚未安装
)

echo.
echo ========================================
echo           操作选项
echo ========================================
echo.
echo 1. 继续监控 (10秒后自动刷新)
echo 2. 配置Android环境 (安装完成后)
echo 3. 验证Flutter环境
echo 4. 退出监控
echo.

choice /c 1234 /t 10 /d 1 /m "请选择操作"

if errorlevel 4 goto EXIT
if errorlevel 3 goto VERIFY_FLUTTER
if errorlevel 2 goto CONFIGURE_ANDROID
if errorlevel 1 goto MONITOR_LOOP

:CONFIGURE_ANDROID
echo.
echo 正在启动Android环境配置...
call "%~dp0configure_android.bat"
goto MONITOR_LOOP

:VERIFY_FLUTTER
echo.
echo 正在验证Flutter环境...
C:\flutter\bin\flutter.bat doctor
echo.
pause
goto MONITOR_LOOP

:EXIT
echo.
echo 监控已停止
echo.
echo 安装完成后请运行:
echo   scripts\configure_android.bat
echo.
pause
exit /b 0
