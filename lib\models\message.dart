class Message {
  final String id;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final bool isFromUser;
  final MessageStatus status;
  final String? audioUrl;
  final String? imageUrl;

  Message({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    required this.isFromUser,
    this.status = MessageStatus.sent,
    this.audioUrl,
    this.imageUrl,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      content: json['content'],
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${json['type']}',
      ),
      timestamp: DateTime.parse(json['timestamp']),
      isFromUser: json['isFromUser'],
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == 'MessageStatus.${json['status']}',
      ),
      audioUrl: json['audioUrl'],
      imageUrl: json['imageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'isFromUser': isFromUser,
      'status': status.toString().split('.').last,
      'audioUrl': audioUrl,
      'imageUrl': imageUrl,
    };
  }
}

enum MessageType {
  text,
  audio,
  image,
  video,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}
