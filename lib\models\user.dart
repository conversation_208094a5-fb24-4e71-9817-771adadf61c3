class User {
  final String id;
  final String name;
  final String avatar;
  final String? email;
  final DateTime createdAt;
  final UserSettings settings;

  User({
    required this.id,
    required this.name,
    required this.avatar,
    this.email,
    required this.createdAt,
    required this.settings,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      avatar: json['avatar'],
      email: json['email'],
      createdAt: DateTime.parse(json['createdAt']),
      settings: UserSettings.fromJson(json['settings']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'email': email,
      'createdAt': createdAt.toIso8601String(),
      'settings': settings.toJson(),
    };
  }
}

class UserSettings {
  final bool voiceEnabled;
  final bool notificationsEnabled;
  final String language;
  final String theme;
  final double voiceSpeed;

  UserSettings({
    this.voiceEnabled = true,
    this.notificationsEnabled = true,
    this.language = 'zh-CN',
    this.theme = 'system',
    this.voiceSpeed = 1.0,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      voiceEnabled: json['voiceEnabled'] ?? true,
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      language: json['language'] ?? 'zh-CN',
      theme: json['theme'] ?? 'system',
      voiceSpeed: json['voiceSpeed']?.toDouble() ?? 1.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'voiceEnabled': voiceEnabled,
      'notificationsEnabled': notificationsEnabled,
      'language': language,
      'theme': theme,
      'voiceSpeed': voiceSpeed,
    };
  }

  UserSettings copyWith({
    bool? voiceEnabled,
    bool? notificationsEnabled,
    String? language,
    String? theme,
    double? voiceSpeed,
  }) {
    return UserSettings(
      voiceEnabled: voiceEnabled ?? this.voiceEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      voiceSpeed: voiceSpeed ?? this.voiceSpeed,
    );
  }
}
