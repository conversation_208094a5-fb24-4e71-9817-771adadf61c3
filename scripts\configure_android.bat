@echo off
chcp 65001 >nul
title Android开发环境配置
color 0A

echo ========================================
echo        Android开发环境配置
echo ========================================
echo.

echo [1/5] 检查Android Studio安装...
set ANDROID_STUDIO_FOUND=0

if exist "%ProgramFiles%\Android\Android Studio\bin\studio64.exe" (
    echo ✓ 找到Android Studio: Program Files
    set ANDROID_STUDIO_FOUND=1
    set "ANDROID_STUDIO_PATH=%ProgramFiles%\Android\Android Studio"
)

if exist "%LOCALAPPDATA%\JetBrains\Toolbox\apps\AndroidStudio" (
    echo ✓ 找到Android Studio: JetBrains Toolbox
    set ANDROID_STUDIO_FOUND=1
)

if %ANDROID_STUDIO_FOUND%==0 (
    echo ✗ 未找到Android Studio安装
    echo.
    echo 请先安装Android Studio:
    echo 1. 访问: https://developer.android.com/studio
    echo 2. 下载并安装Android Studio
    echo 3. 完成初始设置向导
    echo 4. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo [2/5] 检查Android SDK...
if defined ANDROID_HOME (
    echo ✓ ANDROID_HOME已设置: %ANDROID_HOME%
) else (
    echo ! ANDROID_HOME未设置，正在配置...
    
    REM 检查常见的SDK位置
    if exist "%LOCALAPPDATA%\Android\Sdk" (
        setx ANDROID_HOME "%LOCALAPPDATA%\Android\Sdk" /M >nul 2>&1
        echo ✓ 设置ANDROID_HOME: %LOCALAPPDATA%\Android\Sdk
    ) else if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        setx ANDROID_HOME "%USERPROFILE%\AppData\Local\Android\Sdk" /M >nul 2>&1
        echo ✓ 设置ANDROID_HOME: %USERPROFILE%\AppData\Local\Android\Sdk
    ) else (
        echo ! 未找到Android SDK，请在Android Studio中安装
    )
)

echo.
echo [3/5] 配置Android工具PATH...
if defined ANDROID_HOME (
    echo 添加Android工具到PATH...
    setx PATH "%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools\bin" /M >nul 2>&1
    echo ✓ Android工具已添加到PATH
) else (
    echo ! 跳过PATH配置（ANDROID_HOME未设置）
)

echo.
echo [4/5] 验证ADB连接...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo 测试ADB连接...
    "%ANDROID_HOME%\platform-tools\adb.exe" version
    echo ✓ ADB工具可用
) else (
    echo ! ADB工具未找到，请在Android Studio中安装SDK Platform-Tools
)

echo.
echo [5/5] 运行Flutter Doctor...
echo 检查Flutter Android环境...
C:\flutter\bin\flutter.bat doctor

echo.
echo ========================================
echo           配置完成
echo ========================================
echo.
echo 下一步操作:
echo 1. 重启命令提示符
echo 2. 运行: flutter doctor --android-licenses
echo 3. 在Android Studio中创建虚拟设备(AVD)
echo 4. 运行: flutter run
echo.
echo 如果遇到问题:
echo - 确保Android Studio已完成初始设置
echo - 在Android Studio中安装Android SDK
echo - 安装Flutter和Dart插件
echo.
pause
