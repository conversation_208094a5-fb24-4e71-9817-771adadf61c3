import 'dart:convert';
import 'dart:async';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/message.dart';

class WebSocketService {
  static const String wsUrl = 'ws://localhost:8000/ws';
  WebSocketChannel? _channel;
  StreamController<Message>? _messageController;
  StreamController<ConnectionStatus>? _statusController;
  Timer? _heartbeatTimer;
  bool _isConnected = false;

  Stream<Message> get messageStream => _messageController!.stream;
  Stream<ConnectionStatus> get statusStream => _statusController!.stream;
  bool get isConnected => _isConnected;

  WebSocketService() {
    _messageController = StreamController<Message>.broadcast();
    _statusController = StreamController<ConnectionStatus>.broadcast();
  }

  /// 连接WebSocket
  Future<void> connect() async {
    try {
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      _isConnected = true;
      _statusController?.add(ConnectionStatus.connected);

      // 监听消息
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      // 启动心跳
      _startHeartbeat();
    } catch (e) {
      _isConnected = false;
      _statusController?.add(ConnectionStatus.error);
      throw Exception('WebSocket连接失败: $e');
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    _heartbeatTimer?.cancel();
    _isConnected = false;
    await _channel?.sink.close(status.goingAway);
    _statusController?.add(ConnectionStatus.disconnected);
  }

  /// 发送消息
  void sendMessage(Message message) {
    if (_isConnected && _channel != null) {
      _channel!.sink.add(jsonEncode(message.toJson()));
    } else {
      throw Exception('WebSocket未连接');
    }
  }

  /// 发送心跳
  void _sendHeartbeat() {
    if (_isConnected && _channel != null) {
      _channel!.sink.add(jsonEncode({'type': 'heartbeat'}));
    }
  }

  /// 启动心跳定时器
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _sendHeartbeat(),
    );
  }

  /// 处理接收到的消息
  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> json = jsonDecode(data);
      
      if (json['type'] == 'heartbeat') {
        return; // 忽略心跳消息
      }

      final message = Message.fromJson(json);
      _messageController?.add(message);
    } catch (e) {
      print('解析消息失败: $e');
    }
  }

  /// 处理连接错误
  void _onError(error) {
    _isConnected = false;
    _statusController?.add(ConnectionStatus.error);
    print('WebSocket错误: $error');
  }

  /// 处理连接断开
  void _onDisconnected() {
    _isConnected = false;
    _heartbeatTimer?.cancel();
    _statusController?.add(ConnectionStatus.disconnected);
    print('WebSocket连接已断开');
  }

  /// 释放资源
  void dispose() {
    _heartbeatTimer?.cancel();
    _messageController?.close();
    _statusController?.close();
    _channel?.sink.close();
  }
}

enum ConnectionStatus {
  connecting,
  connected,
  disconnected,
  error,
}
