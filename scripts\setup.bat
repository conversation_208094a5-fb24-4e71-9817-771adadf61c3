@echo off
title 数字人女友App - 项目设置
color 0B

echo ========================================
echo      数字人女友App - 项目设置
echo ========================================
echo.

echo [1/6] 检查Flutter环境...
flutter --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ✗ Flutter未安装或未在PATH中
    echo.
    echo 请先安装Flutter环境:
    echo 1. 运行 INSTALL_FLUTTER.bat
    echo 2. 或查看 FLUTTER_INSTALL_GUIDE.md
    echo.
    pause
    exit /b 1
)
echo ✓ Flutter环境检查通过
flutter --version

echo.
echo [2/6] 运行Flutter Doctor...
flutter doctor

echo.
echo [3/6] 清理项目...
flutter clean

echo.
echo [4/6] 获取项目依赖...
flutter pub get

echo.
echo [5/6] 验证项目配置...
if exist "lib\main.dart" (
    echo ✓ 项目文件完整
) else (
    echo ✗ 项目文件不完整
)

if exist "pubspec.lock" (
    echo ✓ 依赖安装成功
) else (
    echo ✗ 依赖安装失败
)

echo.
echo [6/6] 项目设置完成！
echo.
echo ========================================
echo           运行命令
echo ========================================
echo.
echo 开发调试:
echo   flutter run
echo.
echo 发布版本:
echo   flutter run --release
echo.
echo 构建APK:
echo   flutter build apk --release
echo.
echo 构建iOS (需要macOS):
echo   flutter build ios --release
echo.
echo ========================================
echo           注意事项
echo ========================================
echo.
echo 1. 确保已连接Android设备或启动模拟器
echo 2. 首次运行可能需要下载Gradle依赖
echo 3. 如遇到问题，运行: flutter doctor
echo 4. 后端API地址需要在代码中配置
echo.
pause
