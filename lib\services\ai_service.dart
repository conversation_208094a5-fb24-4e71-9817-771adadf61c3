import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/message.dart';

class AIService {
  static const String baseUrl = 'http://localhost:8000'; // 后端API地址
  late final Dio _dio;

  AIService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));
  }

  /// 发送文本消息给AI
  Future<String> sendTextMessage(String message) async {
    try {
      final response = await _dio.post('/api/chat/text', data: {
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
      });

      if (response.statusCode == 200) {
        return response.data['response'] ?? '抱歉，我没有理解您的意思。';
      } else {
        throw Exception('AI服务响应错误: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('发送消息失败: $e');
    }
  }

  /// 发送语音消息给AI
  Future<Map<String, dynamic>> sendAudioMessage(String audioPath) async {
    try {
      FormData formData = FormData.fromMap({
        'audio': await MultipartFile.fromFile(audioPath),
        'timestamp': DateTime.now().toIso8601String(),
      });

      final response = await _dio.post('/api/chat/audio', data: formData);

      if (response.statusCode == 200) {
        return {
          'text': response.data['text'] ?? '',
          'audioUrl': response.data['audioUrl'] ?? '',
        };
      } else {
        throw Exception('AI语音服务响应错误: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('发送语音消息失败: $e');
    }
  }

  /// 获取AI情感状态
  Future<Map<String, dynamic>> getEmotionState() async {
    try {
      final response = await _dio.get('/api/emotion/state');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('获取情感状态失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取情感状态失败: $e');
    }
  }

  /// 更新AI个性设置
  Future<bool> updatePersonality(Map<String, dynamic> settings) async {
    try {
      final response = await _dio.post('/api/personality/update', data: settings);
      return response.statusCode == 200;
    } catch (e) {
      throw Exception('更新个性设置失败: $e');
    }
  }
}
