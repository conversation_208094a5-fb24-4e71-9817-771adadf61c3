# 开发工具安装脚本
# 安装Android Studio, VS Code, Git等开发工具

Write-Host "=== Flutter开发工具安装脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查Chocolatey包管理器
function Install-Chocolatey {
    Write-Host "检查Chocolatey包管理器..." -ForegroundColor Cyan
    try {
        choco --version | Out-Null
        Write-Host "✓ Chocolatey已安装" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "Chocolatey未安装，正在安装..." -ForegroundColor Yellow
        try {
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
            Write-Host "✓ Chocolatey安装成功" -ForegroundColor Green
            return $true
        } catch {
            Write-Host "✗ Chocolatey安装失败" -ForegroundColor Red
            return $false
        }
    }
}

# 安装Git
function Install-Git {
    Write-Host "检查Git..." -ForegroundColor Cyan
    try {
        git --version | Out-Null
        Write-Host "✓ Git已安装" -ForegroundColor Green
    } catch {
        Write-Host "正在安装Git..." -ForegroundColor Yellow
        if ($useChoco) {
            choco install git -y
        } else {
            Write-Host "请手动下载安装Git: https://git-scm.com/download/win" -ForegroundColor Yellow
        }
    }
}

# 安装VS Code
function Install-VSCode {
    Write-Host "检查VS Code..." -ForegroundColor Cyan
    $vscodePath = "${env:ProgramFiles}\Microsoft VS Code\Code.exe"
    if (Test-Path $vscodePath) {
        Write-Host "✓ VS Code已安装" -ForegroundColor Green
    } else {
        Write-Host "正在安装VS Code..." -ForegroundColor Yellow
        if ($useChoco) {
            choco install vscode -y
        } else {
            Write-Host "请手动下载安装VS Code: https://code.visualstudio.com/" -ForegroundColor Yellow
        }
    }
}

# 安装Android Studio
function Install-AndroidStudio {
    Write-Host "检查Android Studio..." -ForegroundColor Cyan
    $androidStudioPaths = @(
        "${env:ProgramFiles}\Android\Android Studio\bin\studio64.exe",
        "${env:LOCALAPPDATA}\JetBrains\Toolbox\apps\AndroidStudio\ch-0\*\bin\studio64.exe"
    )
    
    $found = $false
    foreach ($path in $androidStudioPaths) {
        if (Test-Path $path) {
            Write-Host "✓ Android Studio已安装" -ForegroundColor Green
            $found = $true
            break
        }
    }
    
    if (-not $found) {
        Write-Host "Android Studio未安装" -ForegroundColor Yellow
        Write-Host "请手动下载安装Android Studio: https://developer.android.com/studio" -ForegroundColor Yellow
        Write-Host "安装后需要配置Android SDK" -ForegroundColor Yellow
    }
}

# 配置VS Code Flutter扩展
function Configure-VSCodeExtensions {
    Write-Host "配置VS Code Flutter扩展..." -ForegroundColor Cyan
    try {
        code --install-extension Dart-Code.flutter 2>$null
        code --install-extension Dart-Code.dart-code 2>$null
        Write-Host "✓ VS Code Flutter扩展安装完成" -ForegroundColor Green
    } catch {
        Write-Host "请手动在VS Code中安装Flutter扩展" -ForegroundColor Yellow
    }
}

# 主安装流程
Write-Host "开始安装开发工具..." -ForegroundColor Yellow
Write-Host ""

# 安装Chocolatey
$useChoco = Install-Chocolatey
Write-Host ""

# 安装各种工具
Install-Git
Write-Host ""

Install-VSCode
Write-Host ""

Install-AndroidStudio
Write-Host ""

if (Test-Path "${env:ProgramFiles}\Microsoft VS Code\Code.exe") {
    Configure-VSCodeExtensions
    Write-Host ""
}

# 显示后续步骤
Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "后续配置步骤:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Android Studio配置:" -ForegroundColor White
Write-Host "   - 启动Android Studio" -ForegroundColor Gray
Write-Host "   - 完成初始设置向导" -ForegroundColor Gray
Write-Host "   - 安装Flutter和Dart插件" -ForegroundColor Gray
Write-Host "   - 配置Android SDK" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 接受Android许可证:" -ForegroundColor White
Write-Host "   flutter doctor --android-licenses" -ForegroundColor Gray
Write-Host ""
Write-Host "3. 验证环境:" -ForegroundColor White
Write-Host "   flutter doctor" -ForegroundColor Gray
Write-Host ""
Write-Host "4. 创建虚拟设备 (可选):" -ForegroundColor White
Write-Host "   - 在Android Studio中打开AVD Manager" -ForegroundColor Gray
Write-Host "   - 创建新的虚拟设备" -ForegroundColor Gray
Write-Host ""

Write-Host "按任意键退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
