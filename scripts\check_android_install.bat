@echo off
chcp 65001 >nul
title 检查Android Studio安装状态
color 0E

:CHECK_LOOP
cls
echo ========================================
echo      检查Android Studio安装状态
echo ========================================
echo.

echo 正在检查Android Studio安装...
echo.

REM 检查常见安装位置
set FOUND=0

if exist "%ProgramFiles%\Android\Android Studio\bin\studio64.exe" (
    echo ✓ 找到Android Studio: %ProgramFiles%\Android\Android Studio
    set FOUND=1
)

if exist "%LOCALAPPDATA%\JetBrains\Toolbox\apps\AndroidStudio" (
    echo ✓ 找到Android Studio: JetBrains Toolbox
    set FOUND=1
)

if %FOUND%==1 (
    echo.
    echo ========================================
    echo        Android Studio已安装!
    echo ========================================
    echo.
    echo 现在可以继续配置Android开发环境
    echo.
    set /p continue="是否继续配置Android环境? (y/N): "
    if /i "%continue%"=="y" (
        call "%~dp0configure_android.bat"
        exit /b 0
    ) else (
        echo 配置已取消
        pause
        exit /b 0
    )
) else (
    echo ✗ 未找到Android Studio安装
    echo.
    echo 安装状态:
    echo - 请确保Android Studio安装完成
    echo - 完成初始设置向导
    echo - 安装Android SDK组件
    echo.
    echo 下载地址: https://developer.android.com/studio
    echo.
    set /p retry="重新检查? (y/N): "
    if /i "%retry%"=="y" (
        goto CHECK_LOOP
    ) else (
        echo 退出检查
        pause
        exit /b 1
    )
)
