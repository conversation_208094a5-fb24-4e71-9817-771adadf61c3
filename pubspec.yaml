name: digital_girlfriend_app
description: 数字人女友AI交互应用

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.2
  flutter_screenutil: ^5.9.0
  
  # 状态管理
  provider: ^6.1.1
  get: ^4.6.6
  
  # 网络请求
  dio: ^5.3.2
  web_socket_channel: ^2.4.0
  
  # 音视频
  flutter_webrtc: ^0.9.48
  just_audio: ^0.9.36
  record: ^5.0.4
  permission_handler: ^11.0.1
  
  # 存储
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  
  # 工具类
  intl: ^0.18.1
  uuid: ^4.1.0
  path_provider: ^2.1.1
  
  # 动画
  lottie: ^2.7.0
  flutter_animate: ^4.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/audio/
  
  fonts:
    - family: PingFang
      fonts:
        - asset: assets/fonts/PingFang-Regular.ttf
        - asset: assets/fonts/PingFang-Bold.ttf
          weight: 700
