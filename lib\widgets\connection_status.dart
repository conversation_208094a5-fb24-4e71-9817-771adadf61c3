import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../providers/chat_provider.dart';

class ConnectionStatus extends StatelessWidget {
  const ConnectionStatus({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isConnected) {
          return const SizedBox.shrink(); // 连接正常时不显示
        }

        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 8.h,
          ),
          color: Colors.orange[100],
          child: Row(
            children: [
              Icon(
                Icons.wifi_off,
                size: 16.w,
                color: Colors.orange[700],
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  '连接已断开，正在重新连接...',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.orange[700],
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  chatProvider.reconnect();
                },
                child: Text(
                  '重试',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
