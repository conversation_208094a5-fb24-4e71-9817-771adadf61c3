{"roots": ["digital_girlfriend_app"], "packages": [{"name": "digital_girlfriend_app", "version": "1.0.0+1", "dependencies": ["cupertino_icons", "dio", "flutter", "flutter_animate", "flutter_screenutil", "flutter_webrtc", "get", "intl", "just_audio", "lottie", "path_provider", "permission_handler", "provider", "record", "shared_preferences", "sqflite", "uuid", "web_socket_channel"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "lottie", "version": "2.7.0", "dependencies": ["archive", "flutter", "path", "vector_math"]}, {"name": "intl", "version": "0.18.1", "dependencies": ["clock", "meta", "path"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "flutter_webrtc", "version": "0.9.48+hotfix.1", "dependencies": ["collection", "dart_webrtc", "flutter", "path_provider", "webrtc_interface"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "flutter_screenutil", "version": "5.9.3", "dependencies": ["flutter"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "web_socket_channel", "version": "2.4.5", "dependencies": ["async", "crypto", "stream_channel", "web"]}, {"name": "web", "version": "0.5.1", "dependencies": []}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "record", "version": "5.2.1", "dependencies": ["flutter", "record_android", "record_darwin", "record_linux", "record_platform_interface", "record_web", "record_windows", "uuid"]}, {"name": "record_windows", "version": "1.0.6", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_platform_interface", "version": "1.3.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "record_darwin", "version": "1.2.2", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_android", "version": "1.3.3", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_web", "version": "1.1.9", "dependencies": ["flutter", "flutter_web_plugins", "record_platform_interface", "web"]}, {"name": "record_linux", "version": "0.7.2", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "webrtc_interface", "version": "1.2.2+hotfix.2", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "flutter_animate", "version": "4.5.2", "dependencies": ["flutter", "flutter_shaders"]}, {"name": "flutter_shaders", "version": "0.1.3", "dependencies": ["flutter", "vector_math"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "just_audio", "version": "0.9.44", "dependencies": ["async", "audio_session", "crypto", "flutter", "just_audio_platform_interface", "just_audio_web", "meta", "path", "path_provider", "rxdart", "uuid"]}, {"name": "audio_session", "version": "0.1.25", "dependencies": ["flutter", "flutter_web_plugins", "meta", "rxdart"]}, {"name": "just_audio_web", "version": "0.4.13", "dependencies": ["flutter", "flutter_web_plugins", "just_audio_platform_interface", "web"]}, {"name": "just_audio_platform_interface", "version": "4.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.6", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "shared_preferences_android", "version": "2.4.11", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "dart_webrtc", "version": "1.4.8", "dependencies": ["collection", "js", "logging", "meta", "platform_detect", "synchronized", "web", "webrtc_interface"]}, {"name": "platform_detect", "version": "2.1.5", "dependencies": ["meta", "pub_semver"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}], "configVersion": 1}