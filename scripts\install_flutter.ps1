# Flutter自动安装脚本 (Windows PowerShell)
# 请以管理员身份运行此脚本

param(
    [string]$InstallPath = "C:\flutter",
    [string]$FlutterVersion = "3.16.0"
)

Write-Host "=== Flutter环境自动安装脚本 ===" -ForegroundColor Green
Write-Host "安装路径: $InstallPath" -ForegroundColor Yellow
Write-Host "Flutter版本: $FlutterVersion" -ForegroundColor Yellow
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 请以管理员身份运行此脚本!" -ForegroundColor Red
    Write-Host "右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 1. 检查系统要求
Write-Host "1. 检查系统要求..." -ForegroundColor Cyan
$osVersion = [System.Environment]::OSVersion.Version
if ($osVersion.Major -lt 10) {
    Write-Host "错误: 需要Windows 10或更高版本" -ForegroundColor Red
    exit 1
}
Write-Host "✓ 系统版本检查通过" -ForegroundColor Green

# 2. 检查磁盘空间
Write-Host "2. 检查磁盘空间..." -ForegroundColor Cyan
$drive = (Get-Item $InstallPath.Substring(0,2)).PSDrive
$freeSpace = $drive.Free / 1GB
if ($freeSpace -lt 3) {
    Write-Host "警告: 磁盘空间不足3GB，建议清理磁盘空间" -ForegroundColor Yellow
}
Write-Host "✓ 可用空间: $([math]::Round($freeSpace, 2)) GB" -ForegroundColor Green

# 3. 检查是否已安装Flutter
Write-Host "3. 检查现有Flutter安装..." -ForegroundColor Cyan
try {
    $existingFlutter = flutter --version 2>$null
    if ($existingFlutter) {
        Write-Host "检测到已安装的Flutter:" -ForegroundColor Yellow
        Write-Host $existingFlutter -ForegroundColor Yellow
        $continue = Read-Host "是否继续安装? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            Write-Host "安装已取消" -ForegroundColor Yellow
            exit 0
        }
    }
} catch {
    Write-Host "✓ 未检测到现有Flutter安装" -ForegroundColor Green
}

# 4. 创建安装目录
Write-Host "4. 创建安装目录..." -ForegroundColor Cyan
if (Test-Path $InstallPath) {
    Write-Host "目录已存在: $InstallPath" -ForegroundColor Yellow
    $overwrite = Read-Host "是否覆盖现有目录? (y/N)"
    if ($overwrite -eq "y" -or $overwrite -eq "Y") {
        Remove-Item $InstallPath -Recurse -Force
    } else {
        Write-Host "安装已取消" -ForegroundColor Yellow
        exit 0
    }
}
New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
Write-Host "✓ 创建目录: $InstallPath" -ForegroundColor Green

# 5. 下载Flutter SDK
Write-Host "5. 下载Flutter SDK..." -ForegroundColor Cyan
$downloadUrl = "https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_$FlutterVersion-stable.zip"
$zipPath = "$env:TEMP\flutter_windows_stable.zip"

Write-Host "下载地址: $downloadUrl" -ForegroundColor Yellow
Write-Host "正在下载，请稍候..." -ForegroundColor Yellow

try {
    # 使用.NET WebClient下载，显示进度
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile($downloadUrl, $zipPath)
    Write-Host "✓ 下载完成" -ForegroundColor Green
} catch {
    Write-Host "错误: 下载失败 - $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查网络连接或手动下载" -ForegroundColor Yellow
    exit 1
}

# 6. 解压Flutter SDK
Write-Host "6. 解压Flutter SDK..." -ForegroundColor Cyan
try {
    Expand-Archive -Path $zipPath -DestinationPath (Split-Path $InstallPath) -Force
    Write-Host "✓ 解压完成" -ForegroundColor Green
} catch {
    Write-Host "错误: 解压失败 - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 7. 配置环境变量
Write-Host "7. 配置环境变量..." -ForegroundColor Cyan
$flutterBinPath = "$InstallPath\bin"
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")

if ($currentPath -notlike "*$flutterBinPath*") {
    $newPath = $currentPath + ";$flutterBinPath"
    [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
    Write-Host "✓ 已添加Flutter到系统PATH" -ForegroundColor Green
} else {
    Write-Host "✓ Flutter已在系统PATH中" -ForegroundColor Green
}

# 8. 刷新当前会话的环境变量
$env:Path = [Environment]::GetEnvironmentVariable("Path", "Machine")

# 9. 验证安装
Write-Host "8. 验证Flutter安装..." -ForegroundColor Cyan
try {
    $flutterVersion = & "$flutterBinPath\flutter.bat" --version
    Write-Host "✓ Flutter安装成功!" -ForegroundColor Green
    Write-Host $flutterVersion -ForegroundColor Yellow
} catch {
    Write-Host "错误: Flutter验证失败" -ForegroundColor Red
    Write-Host "请重新启动PowerShell后运行: flutter --version" -ForegroundColor Yellow
}

# 10. 运行Flutter Doctor
Write-Host "9. 运行Flutter Doctor..." -ForegroundColor Cyan
Write-Host "正在检查开发环境..." -ForegroundColor Yellow
try {
    & "$flutterBinPath\flutter.bat" doctor
} catch {
    Write-Host "请重新启动PowerShell后运行: flutter doctor" -ForegroundColor Yellow
}

# 11. 清理临时文件
Write-Host "10. 清理临时文件..." -ForegroundColor Cyan
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
    Write-Host "✓ 清理完成" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Flutter安装完成! ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 重新启动PowerShell或命令提示符" -ForegroundColor White
Write-Host "2. 运行: flutter doctor" -ForegroundColor White
Write-Host "3. 安装Android Studio或VS Code" -ForegroundColor White
Write-Host "4. 运行: flutter doctor --android-licenses (接受Android许可证)" -ForegroundColor White
Write-Host ""
Write-Host "项目运行:" -ForegroundColor Yellow
Write-Host "cd '$((Get-Location).Path)'" -ForegroundColor White
Write-Host "flutter pub get" -ForegroundColor White
Write-Host "flutter run" -ForegroundColor White
Write-Host ""

pause
