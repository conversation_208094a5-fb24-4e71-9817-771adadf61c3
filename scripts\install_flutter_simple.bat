@echo off
title Flutter环境安装脚本
color 0A

echo ========================================
echo        Flutter环境安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 请以管理员身份运行此脚本!
    echo 右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [1/6] 检查系统环境...
echo 系统: %OS%
echo 架构: %PROCESSOR_ARCHITECTURE%
echo.

echo [2/6] 创建Flutter安装目录...
set FLUTTER_PATH=C:\flutter
if exist "%FLUTTER_PATH%" (
    echo 目录已存在: %FLUTTER_PATH%
    set /p overwrite="是否覆盖现有目录? (y/N): "
    if /i "%overwrite%"=="y" (
        rmdir /s /q "%FLUTTER_PATH%"
        echo 已删除现有目录
    ) else (
        echo 安装已取消
        pause
        exit /b 0
    )
)
mkdir "%FLUTTER_PATH%" 2>nul
echo 创建目录: %FLUTTER_PATH%
echo.

echo [3/6] 下载Flutter SDK...
echo 正在下载Flutter 3.16.0 稳定版...
echo 下载地址: https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.16.0-stable.zip
echo.
echo 请手动下载Flutter SDK并解压到 C:\flutter
echo 或者使用PowerShell脚本进行自动下载
echo.
echo 下载步骤:
echo 1. 访问: https://flutter.dev/docs/get-started/install/windows
echo 2. 下载最新稳定版Flutter SDK
echo 3. 解压到 C:\ 目录 (会创建 C:\flutter 文件夹)
echo.
set /p downloaded="下载并解压完成后按任意键继续..."
echo.

echo [4/6] 验证Flutter文件...
if not exist "%FLUTTER_PATH%\bin\flutter.bat" (
    echo 错误: 未找到Flutter可执行文件
    echo 请确保已正确解压Flutter SDK到 %FLUTTER_PATH%
    pause
    exit /b 1
)
echo Flutter文件验证成功
echo.

echo [5/6] 配置环境变量...
:: 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set CURRENT_PATH=%%b

:: 检查Flutter是否已在PATH中
echo %CURRENT_PATH% | findstr /i "flutter\bin" >nul
if %errorLevel% equ 0 (
    echo Flutter已在系统PATH中
) else (
    echo 添加Flutter到系统PATH...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%CURRENT_PATH%;%FLUTTER_PATH%\bin" /f >nul
    if %errorLevel% equ 0 (
        echo 环境变量配置成功
    ) else (
        echo 警告: 环境变量配置失败，请手动添加
    )
)
echo.

echo [6/6] 验证安装...
echo 正在验证Flutter安装...
"%FLUTTER_PATH%\bin\flutter.bat" --version
if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo        Flutter安装成功!
    echo ========================================
    echo.
    echo 下一步操作:
    echo 1. 重新启动命令提示符
    echo 2. 运行: flutter doctor
    echo 3. 安装开发工具 (Android Studio 或 VS Code)
    echo 4. 运行: flutter doctor --android-licenses
    echo.
    echo 项目运行:
    echo cd "%~dp0.."
    echo flutter pub get
    echo flutter run
    echo.
) else (
    echo 错误: Flutter验证失败
    echo 请重新启动命令提示符后运行: flutter --version
)

echo 按任意键退出...
pause >nul
